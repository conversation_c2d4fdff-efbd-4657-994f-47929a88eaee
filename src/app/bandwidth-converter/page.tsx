'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import Footer from "@/components/Footer";
import CalculatorHeader from '@/components/CalculatorHeader';
import { containerStyles, textStyles, inputStyles, statusStyles, cn } from '@/components/ui/styles';

interface ConversionResults {
  bps: number;
  kbps: number;
  mbps: number;
  gbps: number;
  tbps: number;
  kibps: number;
  mibps: number;
  gibps: number;
  tibps: number;
  Bps: number;
  KBps: number;
  MBps: number;
  GBps: number;
  TBps: number;
  KiBps: number;
  MiBps: number;
  GiBps: number;
  TiBps: number;
}

interface DownloadTimeResult {
  seconds: number;
  minutes: number;
  hours: number;
  days: number;
  formattedTime: string;
}

interface WebsiteBandwidthResult {
  dailyBandwidth: number;
  monthlyBandwidth: number;
  requiredSpeed: number;
}

interface HostingBandwidthResult {
  monthlyTransfer: number;
  peakBandwidth: number;
  recommendedPlan: string;
  costEstimate: number;
}

function BandwidthConverterContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [activeTab, setActiveTab] = useState<string>('converter');
  const [inputValue, setInputValue] = useState<string>('100');
  const [fromUnit, setFromUnit] = useState<string>('MBps');
  
  // Download time calculator states
  const [fileSize, setFileSize] = useState<string>('1');
  const [fileSizeUnit, setFileSizeUnit] = useState<string>('gb');
  const [downloadSpeed, setDownloadSpeed] = useState<string>('100');
  const [downloadSpeedUnit, setDownloadSpeedUnit] = useState<string>('MBps');
  const [downloadTime, setDownloadTime] = useState<DownloadTimeResult>({
    seconds: 10,
    minutes: 0.167,
    hours: 0.0028,
    days: 0.00012,
    formattedTime: '10s'
  });
  
  // Website bandwidth calculator states
  const [pageSize, setPageSize] = useState<string>('2');
  const [pageSizeUnit, setPageSizeUnit] = useState<string>('mb');
  const [visitors, setVisitors] = useState<string>('1000');
  const [visitorsUnit, setVisitorsUnit] = useState<string>('day');
  const [pageViews, setPageViews] = useState<string>('3');
  const [redundancyFactor, setRedundancyFactor] = useState<string>('1.5');
  const [websiteBandwidth, setWebsiteBandwidth] = useState<WebsiteBandwidthResult>({
    dailyBandwidth: 6,
    monthlyBandwidth: 180,
    requiredSpeed: 0.104
  });
  
  // Hosting bandwidth calculator states
  const [hostingFileSize, setHostingFileSize] = useState<string>('50');
  const [hostingFileSizeUnit, setHostingFileSizeUnit] = useState<string>('mb');
  const [hostingDownloads, setHostingDownloads] = useState<string>('1000');
  const [hostingUploads, setHostingUploads] = useState<string>('100');
  const [hostingWebTraffic, setHostingWebTraffic] = useState<string>('10');
  const [hostingVideoStreaming, setHostingVideoStreaming] = useState<string>('0');
  const [hostingBandwidth, setHostingBandwidth] = useState<HostingBandwidthResult>({
    monthlyTransfer: 50,
    peakBandwidth: 5.8,
    recommendedPlan: 'Basic',
    costEstimate: 15
  });
  
  const [results, setResults] = useState<ConversionResults>({
    bps: 800000000,
    kbps: 800000,
    mbps: 800,
    gbps: 0.8,
    tbps: 0.0008,
    kibps: 781250,
    mibps: 762.939,
    gibps: 0.745,
    tibps: 0.000728,
    Bps: 100000000,
    KBps: 100000,
    MBps: 100,
    GBps: 0.1,
    TBps: 0.0001,
    KiBps: 97656.25,
    MiBps: 95.3674,
    GiBps: 0.0931,
    TiBps: 0.0000909
  });

  // URL state management
  const updateURL = useCallback((params: Record<string, string>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(params).forEach(([key, value]) => {
      if (value && value !== '') {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });
    router.push(`?${newParams.toString()}`, { scroll: false });
  }, [router, searchParams]);

  // Initialize state from URL parameters
  useEffect(() => {
    const tab = searchParams.get('tab') || 'converter';
    const value = searchParams.get('value') || '100';
    const unit = searchParams.get('unit') || 'MBps';
    const fSize = searchParams.get('fileSize') || '1';
    const fSizeUnit = searchParams.get('fileSizeUnit') || 'gb';
    const dSpeed = searchParams.get('downloadSpeed') || '100';
    const dSpeedUnit = searchParams.get('downloadSpeedUnit') || 'MBps';
    const pSize = searchParams.get('pageSize') || '2';
    const pSizeUnit = searchParams.get('pageSizeUnit') || 'mb';
    const vis = searchParams.get('visitors') || '1000';
    const visUnit = searchParams.get('visitorsUnit') || 'day';
    const pViews = searchParams.get('pageViews') || '3';
    const redundancy = searchParams.get('redundancyFactor') || '1.5';
    const hFileSize = searchParams.get('hostingFileSize') || '50';
    const hFileSizeUnit = searchParams.get('hostingFileSizeUnit') || 'mb';
    const hDownloads = searchParams.get('hostingDownloads') || '1000';
    const hUploads = searchParams.get('hostingUploads') || '100';
    const hWebTraffic = searchParams.get('hostingWebTraffic') || '10';
    const hVideoStreaming = searchParams.get('hostingVideoStreaming') || '0';

    setActiveTab(tab);
    setInputValue(value);
    setFromUnit(unit);
    setFileSize(fSize);
    setFileSizeUnit(fSizeUnit);
    setDownloadSpeed(dSpeed);
    setDownloadSpeedUnit(dSpeedUnit);
    setPageSize(pSize);
    setPageSizeUnit(pSizeUnit);
    setVisitors(vis);
    setVisitorsUnit(visUnit);
    setPageViews(pViews);
    setRedundancyFactor(redundancy);
    setHostingFileSize(hFileSize);
    setHostingFileSizeUnit(hFileSizeUnit);
    setHostingDownloads(hDownloads);
    setHostingUploads(hUploads);
    setHostingWebTraffic(hWebTraffic);
    setHostingVideoStreaming(hVideoStreaming);
  }, [searchParams]);

  // Unit conversion functions
  const convertToAllUnits = useCallback((value: number, unit: string): ConversionResults => {
    const decimalBitFactors = {
      bps: 1,
      kbps: 1000,
      mbps: 1000000,
      gbps: 1000000000,
      tbps: 1000000000000
    };

    const binaryBitFactors = {
      bps: 1,
      kibps: 1024,
      mibps: 1024 * 1024,
      gibps: 1024 * 1024 * 1024,
      tibps: 1024 * 1024 * 1024 * 1024
    };

    const decimalByteFactors = {
      Bps: 1,
      KBps: 1000,
      MBps: 1000000,
      GBps: 1000000000,
      TBps: 1000000000000
    };

    const binaryByteFactors = {
      Bps: 1,
      KiBps: 1024,
      MiBps: 1024 * 1024,
      GiBps: 1024 * 1024 * 1024,
      TiBps: 1024 * 1024 * 1024 * 1024
    };

    let totalBps: number;

    // Convert input to bits per second
    if (unit in decimalBitFactors) {
      totalBps = value * decimalBitFactors[unit as keyof typeof decimalBitFactors];
    } else if (unit in binaryBitFactors) {
      totalBps = value * binaryBitFactors[unit as keyof typeof binaryBitFactors];
    } else if (unit in decimalByteFactors) {
      // Convert bytes to bits (multiply by 8)
      totalBps = value * decimalByteFactors[unit as keyof typeof decimalByteFactors] * 8;
    } else if (unit in binaryByteFactors) {
      // Convert bytes to bits (multiply by 8)
      totalBps = value * binaryByteFactors[unit as keyof typeof binaryByteFactors] * 8;
    } else {
      totalBps = value;
    }

    const totalBytesPerSecond = totalBps / 8;

    return {
      // Bit-based units
      bps: totalBps,
      kbps: totalBps / decimalBitFactors.kbps,
      mbps: totalBps / decimalBitFactors.mbps,
      gbps: totalBps / decimalBitFactors.gbps,
      tbps: totalBps / decimalBitFactors.tbps,
      kibps: totalBps / binaryBitFactors.kibps,
      mibps: totalBps / binaryBitFactors.mibps,
      gibps: totalBps / binaryBitFactors.gibps,
      tibps: totalBps / binaryBitFactors.tibps,
      // Byte-based units
      Bps: totalBytesPerSecond,
      KBps: totalBytesPerSecond / decimalByteFactors.KBps,
      MBps: totalBytesPerSecond / decimalByteFactors.MBps,
      GBps: totalBytesPerSecond / decimalByteFactors.GBps,
      TBps: totalBytesPerSecond / decimalByteFactors.TBps,
      KiBps: totalBytesPerSecond / binaryByteFactors.KiBps,
      MiBps: totalBytesPerSecond / binaryByteFactors.MiBps,
      GiBps: totalBytesPerSecond / binaryByteFactors.GiBps,
      TiBps: totalBytesPerSecond / binaryByteFactors.TiBps
    };
  }, []);

  // Download time calculation
  const calculateDownloadTime = useCallback((fileSizeValue: number, fileSizeUnitValue: string, speedValue: number, speedUnitValue: string): DownloadTimeResult => {
    const fileSizeFactors = {
      'b': 1,
      'kb': 1000,
      'mb': 1000000,
      'gb': 1000000000,
      'tb': 1000000000000
    };

    // Speed factors for bits per second units
    const bitSpeedFactors = {
      'bps': 1,
      'kbps': 1000,
      'mbps': 1000000,
      'gbps': 1000000000,
      'tbps': 1000000000000
    };

    // Speed factors for bytes per second units
    const byteSpeedFactors = {
      'Bps': 1,
      'KBps': 1000,
      'MBps': 1000000,
      'GBps': 1000000000,
      'TBps': 1000000000000
    };

    const fileSizeInBytes = fileSizeValue * fileSizeFactors[fileSizeUnitValue as keyof typeof fileSizeFactors];
    
    let speedInBytesPerSecond: number;
    
    // Determine if the speed unit is bits or bytes based
    if (speedUnitValue in bitSpeedFactors) {
      // Convert bits per second to bytes per second
      const speedInBps = speedValue * bitSpeedFactors[speedUnitValue as keyof typeof bitSpeedFactors];
      speedInBytesPerSecond = speedInBps / 8;
    } else if (speedUnitValue in byteSpeedFactors) {
      // Already in bytes per second
      speedInBytesPerSecond = speedValue * byteSpeedFactors[speedUnitValue as keyof typeof byteSpeedFactors];
    } else {
      // Fallback: assume it's bytes per second
      speedInBytesPerSecond = speedValue;
    }
    
    const totalSeconds = fileSizeInBytes / speedInBytesPerSecond;
    const minutes = totalSeconds / 60;
    const hours = minutes / 60;
    const days = hours / 24;

    let formattedTime = '';
    if (isNaN(totalSeconds) || totalSeconds === 0) {
      formattedTime = '0s';
    } else if (totalSeconds < 60) {
      formattedTime = `${Math.round(totalSeconds)}s`;
    } else if (minutes < 60) {
      const mins = Math.floor(minutes);
      const secs = Math.round(totalSeconds % 60);
      formattedTime = `${mins}m ${secs}s`;
    } else if (hours < 24) {
      const hrs = Math.floor(hours);
      const mins = Math.round(minutes % 60);
      formattedTime = `${hrs}h ${mins}m`;
    } else {
      const dys = Math.floor(days);
      const hrs = Math.round(hours % 24);
      formattedTime = `${dys}d ${hrs}h`;
    }

    return {
      seconds: isNaN(totalSeconds) ? 0 : totalSeconds,
      minutes: isNaN(minutes) ? 0 : minutes,
      hours: isNaN(hours) ? 0 : hours,
      days: isNaN(days) ? 0 : days,
      formattedTime
    };
  }, []);

  // Website bandwidth calculation
  const calculateWebsiteBandwidth = useCallback((
    pageSizeValue: number, 
    pageSizeUnitValue: string,
    visitorsValue: number, 
    visitorsUnitValue: string,
    pageViewsValue: number,
    redundancyFactorValue: number
  ): WebsiteBandwidthResult => {
    const pageSizeFactors = {
      'b': 1,
      'kb': 1000,
      'mb': 1000000,
      'gb': 1000000000,
      'tb': 1000000000000
    };

    const visitorsTimeFactors = {
      'second': 86400, // visitors per second to daily visitors
      'minute': 1440,  // visitors per minute to daily visitors
      'hour': 24,      // visitors per hour to daily visitors
      'day': 1,        // visitors per day to daily visitors
      'week': 1/7,     // visitors per week to daily visitors
      'month': 1/30,   // visitors per month to daily visitors
      'year': 1/365    // visitors per year to daily visitors
    };

    const pageSizeInBytes = pageSizeValue * pageSizeFactors[pageSizeUnitValue as keyof typeof pageSizeFactors];
    const dailyVisitors = visitorsValue * visitorsTimeFactors[visitorsUnitValue as keyof typeof visitorsTimeFactors];
    const dailyTraffic = dailyVisitors * pageViewsValue * pageSizeInBytes;
    const monthlyTraffic = dailyTraffic * 30;
    
    // Calculate required speed (assuming peak traffic is 10% of daily traffic in 1 hour)
    const peakTrafficPerSecond = (dailyTraffic * 0.1) / 3600;
    const requiredSpeedBps = peakTrafficPerSecond * 8; // Convert bytes to bits
    const requiredSpeedMbps = (requiredSpeedBps / 1000000) * redundancyFactorValue; // Apply redundancy factor

    return {
      dailyBandwidth: dailyTraffic / (1024 * 1024 * 1024), // Convert to GB
      monthlyBandwidth: monthlyTraffic / (1024 * 1024 * 1024), // Convert to GB
      requiredSpeed: requiredSpeedMbps
    };
  }, []);

  // Hosting bandwidth calculation
  const calculateHostingBandwidth = useCallback((
    fileSizeValue: number, 
    fileSizeUnitValue: string, 
    downloadsValue: number, 
    uploadsValue: number, 
    webTrafficValue: number, 
    videoStreamingValue: number
  ): HostingBandwidthResult => {
    const fileSizeFactors = {
      'b': 1,
      'kb': 1000,
      'mb': 1000000,
      'gb': 1000000000,
      'tb': 1000000000000
    };

    const fileSizeInBytes = fileSizeValue * fileSizeFactors[fileSizeUnitValue as keyof typeof fileSizeFactors];
    
    // Calculate monthly transfer in GB
    const downloadTraffic = (downloadsValue * fileSizeInBytes) / (1024 * 1024 * 1024);
    const uploadTraffic = (uploadsValue * fileSizeInBytes * 0.1) / (1024 * 1024 * 1024); // Assume uploads are 10% of file size
    const webTrafficGB = webTrafficValue;
    const videoStreamingGB = videoStreamingValue;
    
    const totalMonthlyTransfer = downloadTraffic + uploadTraffic + webTrafficGB + videoStreamingGB;
    
    // Calculate peak bandwidth (assuming 10% of monthly traffic in peak hour)
    const peakHourTraffic = totalMonthlyTransfer * 0.1; // 10% of monthly in peak hour
    const peakBandwidthMbps = (peakHourTraffic * 1024 * 8) / 3600; // Convert GB to Mbps
    
    // Recommend hosting plan
    let recommendedPlan = 'Basic';
    let costEstimate = 10;
    
    if (totalMonthlyTransfer > 1000) {
      recommendedPlan = 'Enterprise';
      costEstimate = 200;
    } else if (totalMonthlyTransfer > 500) {
      recommendedPlan = 'Professional';
      costEstimate = 100;
    } else if (totalMonthlyTransfer > 100) {
      recommendedPlan = 'Business';
      costEstimate = 50;
    } else if (totalMonthlyTransfer > 50) {
      recommendedPlan = 'Standard';
      costEstimate = 25;
    }

    return {
      monthlyTransfer: totalMonthlyTransfer,
      peakBandwidth: peakBandwidthMbps,
      recommendedPlan,
      costEstimate
    };
  }, []);

  // Event handlers with URL updates
  const handleInputChange = (value: string) => {
    setInputValue(value);
    const numValue = parseFloat(value) || 0;
    setResults(convertToAllUnits(numValue, fromUnit));
    updateURL({ value });
  };

  const handleUnitChange = (unit: string) => {
    setFromUnit(unit);
    const numValue = parseFloat(inputValue) || 0;
    setResults(convertToAllUnits(numValue, unit));
    updateURL({ unit });
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    // Clear all URL parameters except the active tab
    const newParams = new URLSearchParams();
    newParams.set('tab', tab);
    router.push(`?${newParams.toString()}`, { scroll: false });
  };

  // Download calculator handlers
  const handleFileSizeChange = (value: string) => {
    setFileSize(value);
    updateURL({ fileSize: value });
  };

  const handleFileSizeUnitChange = (unit: string) => {
    setFileSizeUnit(unit);
    updateURL({ fileSizeUnit: unit });
  };

  const handleDownloadSpeedChange = (value: string) => {
    setDownloadSpeed(value);
    updateURL({ downloadSpeed: value });
  };

  const handleDownloadSpeedUnitChange = (unit: string) => {
    setDownloadSpeedUnit(unit);
    updateURL({ downloadSpeedUnit: unit });
  };

  // Website calculator handlers
  const handlePageSizeChange = (value: string) => {
    setPageSize(value);
    updateURL({ pageSize: value });
  };

  const handlePageSizeUnitChange = (unit: string) => {
    setPageSizeUnit(unit);
    updateURL({ pageSizeUnit: unit });
  };

  const handleVisitorsChange = (value: string) => {
    setVisitors(value);
    updateURL({ visitors: value });
  };

  const handleVisitorsUnitChange = (unit: string) => {
    setVisitorsUnit(unit);
    updateURL({ visitorsUnit: unit });
  };

  const handlePageViewsChange = (value: string) => {
    setPageViews(value);
    updateURL({ pageViews: value });
  };

  const handleRedundancyFactorChange = (value: string) => {
    setRedundancyFactor(value);
    updateURL({ redundancyFactor: value });
  };

  // Hosting calculator handlers
  const handleHostingFileSizeChange = (value: string) => {
    setHostingFileSize(value);
    updateURL({ hostingFileSize: value });
  };

  const handleHostingFileSizeUnitChange = (unit: string) => {
    setHostingFileSizeUnit(unit);
    updateURL({ hostingFileSizeUnit: unit });
  };

  const handleHostingDownloadsChange = (value: string) => {
    setHostingDownloads(value);
    updateURL({ hostingDownloads: value });
  };

  const handleHostingUploadsChange = (value: string) => {
    setHostingUploads(value);
    updateURL({ hostingUploads: value });
  };

  const handleHostingWebTrafficChange = (value: string) => {
    setHostingWebTraffic(value);
    updateURL({ hostingWebTraffic: value });
  };

  const handleHostingVideoStreamingChange = (value: string) => {
    setHostingVideoStreaming(value);
    updateURL({ hostingVideoStreaming: value });
  };

  const handleDownloadCalculation = useCallback(() => {
    const fileSizeValue = parseFloat(fileSize) || 0;
    const speedValue = parseFloat(downloadSpeed) || 0;
    setDownloadTime(calculateDownloadTime(fileSizeValue, fileSizeUnit, speedValue, downloadSpeedUnit));
  }, [fileSize, fileSizeUnit, downloadSpeed, downloadSpeedUnit, calculateDownloadTime]);

  const handleWebsiteCalculation = useCallback(() => {
    const pageSizeValue = parseFloat(pageSize) || 0;
    const visitorsValue = parseFloat(visitors) || 0;
    const pageViewsValue = parseFloat(pageViews) || 0;
    const redundancyFactorValue = parseFloat(redundancyFactor) || 1;
    setWebsiteBandwidth(calculateWebsiteBandwidth(
      pageSizeValue, 
      pageSizeUnit,
      visitorsValue, 
      visitorsUnit,
      pageViewsValue,
      redundancyFactorValue
    ));
  }, [pageSize, pageSizeUnit, visitors, visitorsUnit, pageViews, redundancyFactor, calculateWebsiteBandwidth]);

  const handleHostingCalculation = useCallback(() => {
    const fileSizeValue = parseFloat(hostingFileSize) || 0;
    const downloadsValue = parseFloat(hostingDownloads) || 0;
    const uploadsValue = parseFloat(hostingUploads) || 0;
    const webTrafficValue = parseFloat(hostingWebTraffic) || 0;
    const videoStreamingValue = parseFloat(hostingVideoStreaming) || 0;
    setHostingBandwidth(calculateHostingBandwidth(
      fileSizeValue, 
      hostingFileSizeUnit, 
      downloadsValue, 
      uploadsValue, 
      webTrafficValue, 
      videoStreamingValue
    ));
  }, [hostingFileSize, hostingFileSizeUnit, hostingDownloads, hostingUploads, hostingWebTraffic, hostingVideoStreaming, calculateHostingBandwidth]);

  // Initialize calculations
  useEffect(() => {
    const numValue = parseFloat(inputValue) || 0;
    setResults(convertToAllUnits(numValue, fromUnit));
  }, [inputValue, fromUnit, convertToAllUnits]);

  useEffect(() => {
    handleDownloadCalculation();
  }, [handleDownloadCalculation]);

  useEffect(() => {
    handleWebsiteCalculation();
  }, [handleWebsiteCalculation]);

  useEffect(() => {
    handleHostingCalculation();
  }, [handleHostingCalculation]);

  const formatNumber = (num: number): string => {
    if (num === 0) return '0';
    if (num < 0.000001) return num.toExponential(3);
    if (num < 0.001) return num.toFixed(9).replace(/\.?0+$/, '');
    if (num < 1) return num.toFixed(6).replace(/\.?0+$/, '');
    if (num < 1000) return num.toFixed(3).replace(/\.?0+$/, '');
    return num.toLocaleString('en-US', { maximumFractionDigits: 2 });
  };

  const bandwidthUnits = [
    // Bit-based units (bandwidth)
    { value: 'bps', label: 'bps', fullName: 'Bits per Second', category: 'Bandwidth (bits)' },
    { value: 'kbps', label: 'Kbps', fullName: 'Kilobits per Second', category: 'Bandwidth (bits)' },
    { value: 'mbps', label: 'Mbps', fullName: 'Megabits per Second', category: 'Bandwidth (bits)' },
    { value: 'gbps', label: 'Gbps', fullName: 'Gigabits per Second', category: 'Bandwidth (bits)' },
    { value: 'tbps', label: 'Tbps', fullName: 'Terabits per Second', category: 'Bandwidth (bits)' },
    // Byte-based units (data transfer)
    { value: 'Bps', label: 'B/s', fullName: 'Bytes per Second', category: 'Data Transfer (bytes)' },
    { value: 'KBps', label: 'KB/s', fullName: 'Kilobytes per Second', category: 'Data Transfer (bytes)' },
    { value: 'MBps', label: 'MB/s', fullName: 'Megabytes per Second', category: 'Data Transfer (bytes)' },
    { value: 'GBps', label: 'GB/s', fullName: 'Gigabytes per Second', category: 'Data Transfer (bytes)' },
    { value: 'TBps', label: 'TB/s', fullName: 'Terabytes per Second', category: 'Data Transfer (bytes)' }
  ];

  const fileSizeUnits = [
    { value: 'b', label: 'B', fullName: 'Bytes' },
    { value: 'kb', label: 'KB', fullName: 'Kilobytes' },
    { value: 'mb', label: 'MB', fullName: 'Megabytes' },
    { value: 'gb', label: 'GB', fullName: 'Gigabytes' },
    { value: 'tb', label: 'TB', fullName: 'Terabytes' }
  ];

  const visitorsTimeUnits = [
    { value: 'second', label: '/sec', fullName: 'Per Second' },
    { value: 'minute', label: '/min', fullName: 'Per Minute' },
    { value: 'hour', label: '/hr', fullName: 'Per Hour' },
    { value: 'day', label: '/day', fullName: 'Per Day' },
    { value: 'week', label: '/week', fullName: 'Per Week' },
    { value: 'month', label: '/month', fullName: 'Per Month' },
    { value: 'year', label: '/year', fullName: 'Per Year' }
  ];

  const tabs = [
    { id: 'converter', label: 'Unit Converter', icon: '🔄' },
    { id: 'download', label: 'Download Time', icon: '⏱️' },
    { id: 'website', label: 'Website Bandwidth', icon: '🌐' },
    { id: 'hosting', label: 'Hosting Bandwidth', icon: '🏢' }
  ];

  return (
    <>
      <div className={containerStyles.page}>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-6xl mx-auto">
            <CalculatorHeader
              title="Bandwidth Calculator"
              description="Professional network bandwidth calculator: unit conversion, download time calculation, website bandwidth estimation, and network speed reference"
              category="Other"
              className="text-center mb-12"
            />

            {/* Tab Navigation */}
            <div className={cn(containerStyles.card, "p-6 mb-8")}>
              <div className="flex flex-wrap justify-center gap-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => handleTabChange(tab.id)}
                    className={cn(
                      "px-4 py-2 rounded-lg font-medium transition-all duration-200 flex items-center gap-2",
                      activeTab === tab.id
                        ? "bg-blue-500 dark:bg-blue-600 text-white shadow-lg"
                        : cn("bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600", textStyles.body)
                    )}
                  >
                    <span>{tab.icon}</span>
                    <span className="text-sm">{tab.label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Unit Converter Tab */}
            {activeTab === 'converter' && (
              <div className={cn(containerStyles.card, "p-8 mb-8")}>
                <h2 className={cn(textStyles.h2, "mb-6 text-center")}>Bandwidth Unit Converter</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Input Value</label>
                      <input
                        type="number"
                        value={inputValue}
                        onChange={(e) => handleInputChange(e.target.value)}
                        className={cn(inputStyles.base, "text-lg")}
                        placeholder="Enter bandwidth value"
                        step="any"
                      />
                    </div>
                    <div>
                      <label className={cn(textStyles.label, "block mb-3")}>Select Unit</label>
                      
                      {/* Bandwidth (bits) units */}
                      <div className="mb-4">
                        <h4 className={cn("text-sm font-medium mb-2", textStyles.label)}>Bandwidth (bits per second)</h4>
                        <div className="grid grid-cols-3 gap-2">
                          {bandwidthUnits.filter(unit => unit.category === 'Bandwidth (bits)').map((unit) => (
                            <button
                              key={unit.value}
                              onClick={() => handleUnitChange(unit.value)}
                              className={cn(
                                "p-2 rounded-lg border transition-colors duration-200 text-center",
                                fromUnit === unit.value
                                  ? "bg-blue-500 dark:bg-blue-600 text-white border-blue-500 dark:border-blue-600"
                                  : cn("border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600", "bg-white dark:bg-gray-700", textStyles.body)
                              )}
                            >
                              <div className="font-medium text-sm">{unit.label}</div>
                            </button>
                          ))}
                        </div>
                      </div>

                      {/* Data Transfer (bytes) units */}
                      <div>
                        <h4 className={cn("text-sm font-medium mb-2", textStyles.label)}>Data Transfer (bytes per second)</h4>
                        <div className="grid grid-cols-3 gap-2">
                          {bandwidthUnits.filter(unit => unit.category === 'Data Transfer (bytes)').map((unit) => (
                            <button
                              key={unit.value}
                              onClick={() => handleUnitChange(unit.value)}
                              className={cn(
                                "p-2 rounded-lg border transition-colors duration-200 text-center",
                                fromUnit === unit.value
                                  ? "bg-green-500 dark:bg-green-600 text-white border-green-500 dark:border-green-600"
                                  : cn("border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600", "bg-white dark:bg-gray-700", textStyles.body)
                              )}
                            >
                              <div className="font-medium text-sm">{unit.label}</div>
                            </button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-4")}>Conversion Results</h3>
                    <div className="space-y-4">
                      {/* Bandwidth (bits) results */}
                      <div>
                        <h4 className={cn("text-sm font-medium mb-3", textStyles.label)}>Bandwidth (bits per second)</h4>
                        <div className="space-y-2">
                          {bandwidthUnits.filter(unit => unit.category === 'Bandwidth (bits)').map((unit) => (
                            <div key={unit.value} className={cn("p-3 rounded-lg border", "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700")}>
                              <div className="flex justify-between items-center">
                                <div>
                                  <div className={cn("font-medium", textStyles.body)}>{unit.label}</div>
                                  <div className={cn("text-xs", textStyles.muted)}>{unit.fullName}</div>
                                </div>
                                <div className={cn("text-lg font-bold", "text-blue-600 dark:text-blue-400")}>
                                  {formatNumber(results[unit.value as keyof ConversionResults])}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Data Transfer (bytes) results */}
                      <div>
                        <h4 className={cn("text-sm font-medium mb-3", textStyles.label)}>Data Transfer (bytes per second)</h4>
                        <div className="space-y-2">
                          {bandwidthUnits.filter(unit => unit.category === 'Data Transfer (bytes)').map((unit) => (
                            <div key={unit.value} className={cn("p-3 rounded-lg border", "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700")}>
                              <div className="flex justify-between items-center">
                                <div>
                                  <div className={cn("font-medium", textStyles.body)}>{unit.label}</div>
                                  <div className={cn("text-xs", textStyles.muted)}>{unit.fullName}</div>
                                </div>
                                <div className={cn("text-lg font-bold", "text-green-600 dark:text-green-400")}>
                                  {formatNumber(results[unit.value as keyof ConversionResults])}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Download Time Calculator Tab */}
            {activeTab === 'download' && (
              <div className={cn(containerStyles.card, "p-8 mb-8")}>
                <h2 className={cn(textStyles.h2, "mb-6 text-center")}>Download Time Calculator</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <label className={cn(textStyles.label, "block mb-3")}>File Size</label>
                      <div className="relative">
                        <input
                          type="number"
                          value={fileSize}
                          onChange={(e) => handleFileSizeChange(e.target.value)}
                          className={cn(inputStyles.base, "pr-16")}
                          placeholder="Enter file size"
                          step="any"
                        />
                        <select
                          value={fileSizeUnit}
                          onChange={(e) => handleFileSizeUnitChange(e.target.value)}
                          className={cn("absolute right-2 top-1/2 -translate-y-1/2 bg-transparent border-none text-sm font-medium focus:outline-none focus:ring-0", textStyles.body)}
                        >
                          {fileSizeUnits.map((unit) => (
                            <option key={unit.value} value={unit.value}>{unit.label}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className={cn(textStyles.label, "block mb-3")}>Download Speed</label>
                      <div className="relative">
                        <input
                          type="number"
                          value={downloadSpeed}
                          onChange={(e) => handleDownloadSpeedChange(e.target.value)}
                          className={cn(inputStyles.base, "pr-20")}
                          placeholder="Enter download speed"
                          step="any"
                        />
                        <select
                          value={downloadSpeedUnit}
                          onChange={(e) => handleDownloadSpeedUnitChange(e.target.value)}
                          className={cn("absolute right-2 top-1/2 -translate-y-1/2 bg-transparent border-none text-sm font-medium focus:outline-none focus:ring-0", textStyles.body)}
                        >
                          {bandwidthUnits.map((unit) => (
                            <option key={unit.value} value={unit.value}>{unit.label}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700")}>
                      <h4 className={cn("font-medium mb-2", textStyles.h4)}>Quick Examples</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Movie (4K, 2h)</span>
                          <span className={textStyles.muted}>~15 GB</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Game Download</span>
                          <span className={textStyles.muted}>20-80 GB</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Software Update</span>
                          <span className={textStyles.muted}>500 MB - 5 GB</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-4")}>Estimated Download Time</h3>
                    <div className={cn("p-6 rounded-lg text-center mb-4", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className={cn("text-3xl font-bold mb-2", "text-blue-600 dark:text-blue-400")}>
                        {downloadTime.formattedTime}
                      </div>
                      <div className={cn("text-sm space-y-1", textStyles.muted)}>
                        <div>Total seconds: {formatNumber(downloadTime.seconds)}</div>
                        <div>Total minutes: {formatNumber(downloadTime.minutes)}</div>
                        <div>Total hours: {formatNumber(downloadTime.hours)}</div>
                        {downloadTime.days > 1 && <div>Total days: {formatNumber(downloadTime.days)}</div>}
                      </div>
                    </div>
                    <div className={cn("p-4 rounded-lg", "bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800")}>
                      <div className={cn("text-sm", textStyles.body)}>
                        <div className="font-medium mb-1">💡 Actual Download Speed</div>
                        <div>Real download speeds are typically 70-80% of theoretical bandwidth</div>
                        <div>For example: 100 Mbps = 12.5 MB/s theoretical, ~9-10 MB/s actual</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Website Bandwidth Calculator Tab */}
            {activeTab === 'website' && (
              <div className={cn(containerStyles.card, "p-8 mb-8")}>
                <h2 className={cn(textStyles.h2, "mb-6 text-center")}>Website Bandwidth Estimator</h2>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                  <div className="space-y-6">
                    <div>
                      <label className={cn(textStyles.label, "block mb-3")}>Average Page Size</label>
                      <div className="relative">
                        <input
                          type="number"
                          value={pageSize}
                          onChange={(e) => handlePageSizeChange(e.target.value)}
                          className={cn(inputStyles.base, "pr-16")}
                          placeholder="Enter page size"
                          step="any"
                        />
                        <select
                          value={pageSizeUnit}
                          onChange={(e) => handlePageSizeUnitChange(e.target.value)}
                          className={cn("absolute right-2 top-1/2 -translate-y-1/2 bg-transparent border-none text-sm font-medium focus:outline-none focus:ring-0", textStyles.body)}
                        >
                          {fileSizeUnits.map((unit) => (
                            <option key={unit.value} value={unit.value}>{unit.label}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className={cn(textStyles.label, "block mb-3")}>Visitor Rate</label>
                      <div className="relative">
                        <input
                          type="number"
                          value={visitors}
                          onChange={(e) => handleVisitorsChange(e.target.value)}
                          className={cn(inputStyles.base, "pr-20")}
                          placeholder="Number of visitors"
                          step="any"
                        />
                        <select
                          value={visitorsUnit}
                          onChange={(e) => handleVisitorsUnitChange(e.target.value)}
                          className={cn("absolute right-2 top-1/2 -translate-y-1/2 bg-transparent border-none text-sm font-medium focus:outline-none focus:ring-0", textStyles.body)}
                        >
                          {visitorsTimeUnits.map((unit) => (
                            <option key={unit.value} value={unit.value}>{unit.label}</option>
                          ))}
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Page Views per Visitor</label>
                      <input
                        type="number"
                        value={pageViews}
                        onChange={(e) => handlePageViewsChange(e.target.value)}
                        className={cn(inputStyles.base)}
                        placeholder="Average page views per visitor"
                        step="0.1"
                      />
                    </div>
                    <div>
                      <label className={cn(textStyles.label, "block mb-2")}>Redundancy Factor</label>
                      <input
                        type="number"
                        value={redundancyFactor}
                        onChange={(e) => handleRedundancyFactorChange(e.target.value)}
                        className={cn(inputStyles.base)}
                        placeholder="Safety margin multiplier"
                        step="0.1"
                        min="1"
                      />
                      <div className={cn("text-xs mt-1", textStyles.muted)}>
                        Recommended: 1.5-3.0 for safety margin
                      </div>
                    </div>
                  </div>
                  <div className="lg:col-span-2">
                    <h3 className={cn(textStyles.h3, "mb-4")}>Bandwidth Requirements</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div className={cn("p-4 rounded-lg text-center", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                        <div className={cn("font-medium", textStyles.body)}>Daily Traffic</div>
                        <div className={cn("text-xl font-bold", "text-green-600 dark:text-green-400")}>
                          {formatNumber(websiteBandwidth.dailyBandwidth)} GB
                        </div>
                      </div>
                      <div className={cn("p-4 rounded-lg text-center", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                        <div className={cn("font-medium", textStyles.body)}>Monthly Traffic</div>
                        <div className={cn("text-xl font-bold", "text-blue-600 dark:text-blue-400")}>
                          {formatNumber(websiteBandwidth.monthlyBandwidth)} GB
                        </div>
                      </div>
                      <div className={cn("p-4 rounded-lg text-center", "bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800")}>
                        <div className={cn("font-medium", textStyles.body)}>Required Bandwidth</div>
                        <div className={cn("text-xl font-bold", "text-orange-600 dark:text-orange-400")}>
                          {formatNumber(websiteBandwidth.requiredSpeed)} Mbps
                        </div>
                      </div>
                    </div>
                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700")}>
                      <div className={cn("text-sm", textStyles.body)}>
                        <div className="font-medium mb-2">📝 Calculation Details</div>
                        <div className="space-y-1">
                          <div>• Peak traffic assumption: 10% of daily traffic in 1 hour</div>
                          <div>• Redundancy factor: {redundancyFactor}x safety margin applied</div>
                          <div>• Actual needs may be lower with caching/CDN</div>
                          <div>• Consider burst traffic and seasonal variations</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className={cn("p-4 rounded-lg", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                      <div className={cn("text-sm", textStyles.body)}>
                        <div className="font-medium mb-2">💡 Redundancy Factor Guide</div>
                        <div className="space-y-1">
                          <div>• 1.0: No safety margin (not recommended)</div>
                          <div>• 1.5: Standard safety margin for stable traffic</div>
                          <div>• 2.0: High safety margin for growing sites</div>
                          <div>• 3.0+: Ultra-safe for mission-critical applications</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Hosting Bandwidth Calculator Tab */}
            {activeTab === 'hosting' && (
              <div className={cn(containerStyles.card, "p-8 mb-8")}>
                <h2 className={cn(textStyles.h2, "mb-6 text-center")}>Hosting Bandwidth Calculator</h2>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <h3 className={cn(textStyles.h3, "mb-4")}>File Download/Upload Traffic</h3>
                      <div className="space-y-4">
                        <div>
                          <label className={cn(textStyles.label, "block mb-3")}>Average File Size</label>
                          <div className="relative">
                            <input
                              type="number"
                              value={hostingFileSize}
                              onChange={(e) => handleHostingFileSizeChange(e.target.value)}
                              className={cn(inputStyles.base, "pr-16")}
                              placeholder="Enter file size"
                              step="any"
                            />
                            <select
                              value={hostingFileSizeUnit}
                              onChange={(e) => handleHostingFileSizeUnitChange(e.target.value)}
                              className={cn("absolute right-2 top-1/2 -translate-y-1/2 bg-transparent border-none text-sm font-medium focus:outline-none focus:ring-0", textStyles.body)}
                            >
                              {fileSizeUnits.map((unit) => (
                                <option key={unit.value} value={unit.value}>{unit.label}</option>
                              ))}
                            </select>
                          </div>
                        </div>
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <label className={cn(textStyles.label, "block mb-2")}>Monthly Downloads</label>
                            <input
                              type="number"
                              value={hostingDownloads}
                              onChange={(e) => handleHostingDownloadsChange(e.target.value)}
                              className={cn(inputStyles.base)}
                              placeholder="Downloads"
                              step="1"
                            />
                          </div>
                          <div>
                            <label className={cn(textStyles.label, "block mb-2")}>Monthly Uploads</label>
                            <input
                              type="number"
                              value={hostingUploads}
                              onChange={(e) => handleHostingUploadsChange(e.target.value)}
                              className={cn(inputStyles.base)}
                              placeholder="Uploads"
                              step="1"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className={cn(textStyles.h3, "mb-4")}>Additional Traffic</h3>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className={cn(textStyles.label, "block mb-2")}>Web Traffic</label>
                          <div className="relative">
                            <input
                              type="number"
                              value={hostingWebTraffic}
                              onChange={(e) => handleHostingWebTrafficChange(e.target.value)}
                              className={cn(inputStyles.base, "pr-12")}
                              placeholder="0"
                              step="0.1"
                            />
                            <span className={cn("absolute right-3 top-1/2 -translate-y-1/2 text-sm", textStyles.muted)}>GB</span>
                          </div>
                        </div>
                        <div>
                          <label className={cn(textStyles.label, "block mb-2")}>Video Streaming</label>
                          <div className="relative">
                            <input
                              type="number"
                              value={hostingVideoStreaming}
                              onChange={(e) => handleHostingVideoStreamingChange(e.target.value)}
                              className={cn(inputStyles.base, "pr-12")}
                              placeholder="0"
                              step="0.1"
                            />
                            <span className={cn("absolute right-3 top-1/2 -translate-y-1/2 text-sm", textStyles.muted)}>GB</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className={cn("p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700")}>
                      <h4 className={cn("font-medium mb-2", textStyles.h4)}>Typical Usage Examples</h4>
                      <div className="space-y-1 text-sm">
                        <div>• Software downloads: 100 MB - 5 GB files</div>
                        <div>• Document sharing: 1-50 MB files</div>
                        <div>• Image galleries: 2-10 MB per image</div>
                        <div>• Video content: 100 MB - 2 GB per video</div>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-4")}>Hosting Requirements</h3>
                    <div className="space-y-4 mb-6">
                      <div className={cn("p-4 rounded-lg text-center", "bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800")}>
                        <div className={cn("font-medium", textStyles.body)}>Monthly Transfer</div>
                        <div className={cn("text-2xl font-bold", "text-blue-600 dark:text-blue-400")}>
                          {formatNumber(hostingBandwidth.monthlyTransfer)} GB
                        </div>
                      </div>
                      <div className={cn("p-4 rounded-lg text-center", "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800")}>
                        <div className={cn("font-medium", textStyles.body)}>Peak Bandwidth</div>
                        <div className={cn("text-2xl font-bold", "text-green-600 dark:text-green-400")}>
                          {formatNumber(hostingBandwidth.peakBandwidth)} Mbps
                        </div>
                      </div>
                    </div>
                    
                    <div className={cn("p-6 rounded-lg", "bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800")}>
                      <h4 className={cn("font-bold text-lg mb-2", "text-purple-600 dark:text-purple-400")}>
                        Recommended Plan: {hostingBandwidth.recommendedPlan}
                      </h4>
                      <div className={cn("text-xl font-bold mb-3", "text-purple-700 dark:text-purple-300")}>
                        ${formatNumber(hostingBandwidth.costEstimate)}/month
                      </div>
                      <div className={cn("text-sm", textStyles.body)}>
                        Based on your bandwidth requirements and typical hosting provider pricing.
                      </div>
                    </div>
                    
                    <div className={cn("mt-6 p-4 rounded-lg", "bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700")}>
                      <div className={cn("text-sm", textStyles.body)}>
                        <div className="font-medium mb-2">📊 Plan Tiers</div>
                        <div className="space-y-1">
                          <div>• Basic: Up to 50 GB/month - $10/month</div>
                          <div>• Standard: Up to 100 GB/month - $25/month</div>
                          <div>• Business: Up to 500 GB/month - $50/month</div>
                          <div>• Professional: Up to 1 TB/month - $100/month</div>
                          <div>• Enterprise: 1+ TB/month - $200+/month</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Network Speed Reference - Always visible below main calculator */}
            <div className="space-y-8">
              <div className={cn(containerStyles.card, "p-6")}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Common Network Connection Speeds</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-4")}>Fixed Networks</h3>
                    <div className="space-y-2">
                      {[
                        { name: 'Dial-up', speed: '56 Kbps', download: '7 KB/s' },
                        { name: 'ADSL', speed: '1-8 Mbps', download: '125KB-1MB/s' },
                        { name: 'Fiber Internet', speed: '50-1000 Mbps', download: '6.25-125 MB/s' },
                        { name: 'Gigabit Internet', speed: '1000 Mbps', download: '125 MB/s' },
                        { name: '10 Gigabit Network', speed: '10 Gbps', download: '1.25 GB/s' }
                      ].map((item, index) => (
                        <div key={index} className={cn("p-3 rounded-lg border", "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600")}>
                          <div className="flex justify-between items-center">
                            <div className={cn("font-medium", textStyles.body)}>{item.name}</div>
                            <div className="text-right">
                              <div className={cn("text-sm font-bold", textStyles.h4)}>{item.speed}</div>
                              <div className={cn("text-xs", textStyles.muted)}>{item.download}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-4")}>Mobile Networks</h3>
                    <div className="space-y-2">
                      {[
                        { name: '2G (GPRS)', speed: '56-114 Kbps', download: '7-14 KB/s' },
                        { name: '3G (HSPA)', speed: '1-10 Mbps', download: '125KB-1.25MB/s' },
                        { name: '4G (LTE)', speed: '10-100 Mbps', download: '1.25-12.5 MB/s' },
                        { name: '4G+ (LTE-A)', speed: '100-300 Mbps', download: '12.5-37.5 MB/s' },
                        { name: '5G', speed: '100-1000 Mbps', download: '12.5-125 MB/s' }
                      ].map((item, index) => (
                        <div key={index} className={cn("p-3 rounded-lg border", "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600")}>
                          <div className="flex justify-between items-center">
                            <div className={cn("font-medium", textStyles.body)}>{item.name}</div>
                            <div className="text-right">
                              <div className={cn("text-sm font-bold", textStyles.h4)}>{item.speed}</div>
                              <div className={cn("text-xs", textStyles.muted)}>{item.download}</div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className={cn(containerStyles.card, "p-6")}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Application Bandwidth Requirements</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-4")}>Video Streaming</h3>
                    <div className="space-y-2">
                      {[
                        { quality: 'Standard Definition (480p)', bandwidth: '3-5 Mbps' },
                        { quality: 'High Definition (720p)', bandwidth: '5-10 Mbps' },
                        { quality: 'Full HD (1080p)', bandwidth: '10-20 Mbps' },
                        { quality: '4K Ultra HD', bandwidth: '25-40 Mbps' },
                        { quality: '8K', bandwidth: '100+ Mbps' }
                      ].map((item, index) => (
                        <div key={index} className={cn("p-3 rounded-lg border", "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600")}>
                          <div className="flex justify-between items-center">
                            <div className={cn("font-medium", textStyles.body)}>{item.quality}</div>
                            <div className={cn("text-sm font-bold", textStyles.h4)}>{item.bandwidth}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h3 className={cn(textStyles.h3, "mb-4")}>Online Gaming</h3>
                    <div className="space-y-2">
                      {[
                        { type: 'Turn-based Games', bandwidth: '1-3 Mbps' },
                        { type: 'Real-time Strategy', bandwidth: '3-5 Mbps' },
                        { type: 'First-person Shooter', bandwidth: '5-10 Mbps' },
                        { type: 'Massively Multiplayer', bandwidth: '10-25 Mbps' },
                        { type: 'Cloud Gaming', bandwidth: '25-50 Mbps' }
                      ].map((item, index) => (
                        <div key={index} className={cn("p-3 rounded-lg border", "bg-white dark:bg-gray-700 border-gray-200 dark:border-gray-600")}>
                          <div className="flex justify-between items-center">
                            <div className={cn("font-medium", textStyles.body)}>{item.type}</div>
                            <div className={cn("text-sm font-bold", textStyles.h4)}>{item.bandwidth}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              <div className={cn(containerStyles.card, "p-6")}>
                <h2 className={cn(textStyles.h2, "mb-6")}>Hosting Plan Comparison</h2>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className={cn("border-b border-gray-300 dark:border-gray-600")}>
                        <th className={cn("text-left p-3", textStyles.h4)}>Plan Type</th>
                        <th className={cn("text-left p-3", textStyles.h4)}>Monthly Transfer</th>
                        <th className={cn("text-left p-3", textStyles.h4)}>Bandwidth</th>
                        <th className={cn("text-left p-3", textStyles.h4)}>Best For</th>
                        <th className={cn("text-left p-3", textStyles.h4)}>Price Range</th>
                      </tr>
                    </thead>
                    <tbody>
                      {[
                        { plan: 'Basic Shared', transfer: '50-100 GB', bandwidth: '5-10 Mbps', bestFor: 'Personal blogs, small websites', price: '$5-15/month' },
                        { plan: 'Premium Shared', transfer: '100-500 GB', bandwidth: '10-50 Mbps', bestFor: 'Business websites, portfolios', price: '$15-30/month' },
                        { plan: 'VPS', transfer: '500GB-2TB', bandwidth: '50-100 Mbps', bestFor: 'High-traffic sites, apps', price: '$20-100/month' },
                        { plan: 'Dedicated', transfer: '2TB-10TB', bandwidth: '100-1000 Mbps', bestFor: 'Enterprise, large applications', price: '$100-500/month' },
                        { plan: 'Cloud Hosting', transfer: 'Pay-per-use', bandwidth: 'Scalable', bestFor: 'Variable traffic, global scale', price: '$0.05-0.15/GB' }
                      ].map((row, index) => (
                        <tr key={index} className={cn("border-b border-gray-200 dark:border-gray-700")}>
                          <td className={cn("p-3 font-medium", textStyles.body)}>{row.plan}</td>
                          <td className={cn("p-3", textStyles.body)}>{row.transfer}</td>
                          <td className={cn("p-3", textStyles.body)}>{row.bandwidth}</td>
                          <td className={cn("p-3", textStyles.bodySmall)}>{row.bestFor}</td>
                          <td className={cn("p-3 font-medium", textStyles.body)}>{row.price}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>

              <div className={cn(statusStyles.info.container)}>
                <h3 className={cn(statusStyles.info.title, "mb-4")}>💡 Practical Tips</h3>
                <div className="space-y-3">
                  <div className={cn("p-3 rounded-lg", "bg-blue-50 dark:bg-blue-900/20")}>
                    <div className="font-medium mb-1">Bandwidth vs Data Transfer</div>
                    <div className="text-sm">Bandwidth is measured in bits per second (bps, Kbps, Mbps), while data transfer is in bytes per second (B/s, KB/s, MB/s). Since 1 byte = 8 bits, divide bandwidth by 8 to get transfer rate. Example: 100 Mbps ÷ 8 = 12.5 MB/s actual speed.</div>
                  </div>
                  <div className={cn("p-3 rounded-lg", "bg-green-50 dark:bg-green-900/20")}>
                    <div className="font-medium mb-1">Real-world Performance</div>
                    <div className="text-sm">Actual transfer speeds are typically 70-80% of theoretical bandwidth due to protocol overhead, network latency, and other factors.</div>
                  </div>
                  <div className={cn("p-3 rounded-lg", "bg-orange-50 dark:bg-orange-900/20")}>
                    <div className="font-medium mb-1">Choosing Bandwidth</div>
                    <div className="text-sm">Consider simultaneous users, peak traffic periods, and future growth when selecting bandwidth. Reserve 20-30% overhead for reliability.</div>
                  </div>
                  <div className={cn("p-3 rounded-lg", "bg-purple-50 dark:bg-purple-900/20")}>
                    <div className="font-medium mb-1">Hosting Considerations</div>
                    <div className="text-sm">Factor in file downloads, uploads, streaming, and peak usage when choosing hosting plans. Many providers offer &quot;unlimited&quot; bandwidth with fair usage policies.</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}

export default function BandwidthConverter() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <BandwidthConverterContent />
    </Suspense>
  );
}