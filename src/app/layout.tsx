import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import <PERSON>ript from "next/script";
import "./globals.css";
import { generateCanonicalUrl } from "@/lib/utils";
import { ThemeProvider } from "@/contexts/ThemeContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Calc9 - Professional Calculator Tools",
  description: "Professional calculator tools including body type calculator, BMI calculator, and more. Fast, accurate, and mobile-friendly calculators for all your needs.",
  keywords: "calculator, body type calculator, BMI calculator, health calculator, fitness calculator",
  authors: [{ name: "<PERSON>c<PERSON>" }],
  robots: "index, follow",
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/logo.svg', type: 'image/svg+xml' },
      { url: '/android-chrome-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/android-chrome-512x512.png', sizes: '512x512', type: 'image/png' }
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
    ],
    shortcut: '/favicon.ico'
  },
  openGraph: {
    title: "Calc9 - Professional Calculator Tools",
    description: "Professional calculator tools including body type calculator, BMI calculator, and more. Fast, accurate, and mobile-friendly calculators for all your needs.",
    url: generateCanonicalUrl("/"),
    siteName: 'Calc9',
    images: [
      {
        url: generateCanonicalUrl("/logo.png"),
        width: 1200,
        height: 630,
        alt: 'Calc9 - Professional Calculator Tools',
      }
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "Calc9 - Professional Calculator Tools",
    description: "Professional calculator tools including body type calculator, BMI calculator, and more. Fast, accurate, and mobile-friendly calculators for all your needs.",
    images: [generateCanonicalUrl("/logo.png")],
  },
  alternates: {
    canonical: generateCanonicalUrl("/"),
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Additional favicon declarations for better browser support */}
        <link rel="icon" href="/favicon.ico" sizes="48x48" />
        <link rel="icon" href="/logo.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" sizes="180x180" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon.ico" />
        <link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png" />
        <link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png" />
        <link rel="manifest" href="/site.webmanifest" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                try {
                  var theme = localStorage.getItem('theme');
                  var systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                  var resolvedTheme = theme === 'system' ? (systemPrefersDark ? 'dark' : 'light') : (theme || 'system');
                  if (resolvedTheme === 'system') {
                    resolvedTheme = systemPrefersDark ? 'dark' : 'light';
                  }
                  if (resolvedTheme === 'dark') {
                    document.documentElement.classList.add('dark');
                  }
                } catch (e) {}
              })();
            `,
          }}
        />
        <script 
          defer 
          src="https://umami.frytea.com/script.js" 
          data-domains="calc9.com,www.calc.com"
          data-website-id="42c553f5-f16b-4e5e-a047-dc6560d2d1c5"></script>
        <script
          src="https://rybbit.frytea.com/api/script.js"
          data-site-id="12"
          defer
        ></script>
        {/* Microsoft Clarity */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                  c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                  t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                  y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "s7z8cfjgts");
            `,
          }}
        />
        <script
          async
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-7296634171837358"
          crossOrigin="anonymous"
        ></script>
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300`}
      >
        <ThemeProvider>
          {children}
        </ThemeProvider>

        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-4PYPLYHM3P"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-4PYPLYHM3P');
          `}
        </Script>
      </body>
    </html>
  );
}
