// Tailwind CSS 暗黑模式最佳实践 - 全局样式抽象

// 容器样式 - 通透玻璃风格设计
export const containerStyles = {
  // 主要容器 - 页面级别
  page: "min-h-screen py-8",
  
  // 卡片容器 - 磨砂玻璃效果
  card: "bg-white/80 dark:bg-white/5 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 dark:border-white/10 ring-1 ring-black/5 dark:ring-white/5",
  cardSmall: "bg-white/70 dark:bg-white/5 backdrop-blur-lg rounded-xl shadow-lg border border-white/20 dark:border-white/10 ring-1 ring-black/5 dark:ring-white/5",
  
  // 表单容器 - 更强的磨砂效果
  form: "bg-white/90 dark:bg-white/10 backdrop-blur-2xl rounded-2xl shadow-2xl p-6 border border-white/30 dark:border-white/20 ring-1 ring-black/5 dark:ring-white/10",
  
  // 信息卡片容器 - 柔和背景
  infoCard: "bg-gradient-to-br from-white/60 to-gray-50/80 dark:from-white/5 dark:to-white/10 backdrop-blur-lg rounded-xl p-6 border border-white/30 dark:border-white/10 shadow-lg",
} as const;

// 文本样式
export const textStyles = {
  // 标题
  h1: "text-4xl font-bold text-gray-900 dark:text-white",
  h2: "text-2xl font-semibold text-gray-900 dark:text-white",
  h3: "text-xl font-semibold text-gray-900 dark:text-white",
  h4: "text-lg font-medium text-gray-900 dark:text-white",
  
  // 正文
  body: "text-gray-700 dark:text-gray-300",
  bodySmall: "text-sm text-gray-600 dark:text-gray-400",
  
  // 标签
  label: "text-sm font-medium text-gray-700 dark:text-gray-300",
  
  // 次要文本
  muted: "text-gray-500 dark:text-gray-400",
} as const;

// 输入组件样式 - 通透设计
export const inputStyles = {
  // 基础输入框
  base: "w-full px-4 py-3 border border-white/30 dark:border-white/20 rounded-xl bg-white/50 dark:bg-white/10 backdrop-blur-md text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 shadow-sm",
  
  // 选择框
  select: "w-full px-4 py-3 border border-white/30 dark:border-white/20 rounded-xl bg-white/50 dark:bg-white/10 backdrop-blur-md text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all duration-200 shadow-sm",
} as const;

// 按钮样式 - 现代渐变玻璃风格
export const buttonStyles = {
  // 主要按钮
  primary: "bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",
  
  // 次要按钮
  secondary: "bg-white/20 dark:bg-white/10 backdrop-blur-md border border-white/30 dark:border-white/20 hover:bg-white/30 dark:hover:bg-white/20 text-gray-700 dark:text-gray-200 font-semibold py-3 px-6 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500/50 focus:ring-offset-2 shadow-md hover:shadow-lg",
  
  // 大按钮
  primaryLarge: "w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold py-4 px-8 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 shadow-xl hover:shadow-2xl transform hover:scale-[1.01] active:scale-[0.99]",
  
  secondaryLarge: "w-full bg-white/20 dark:bg-white/10 backdrop-blur-md border border-white/30 dark:border-white/20 hover:bg-white/30 dark:hover:bg-white/20 text-gray-700 dark:text-gray-200 font-bold py-4 px-8 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500/50 focus:ring-offset-2 shadow-lg hover:shadow-xl",
  
  // 计算器专用按钮 - 现代玻璃风格
  calc: {
    // 数字按钮
    number: "bg-white/60 dark:bg-white/10 backdrop-blur-md border border-white/30 dark:border-white/20 hover:bg-white/80 dark:hover:bg-white/20 text-gray-800 dark:text-white font-bold py-4 px-4 rounded-xl text-lg transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500/30 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95",
    
    // 运算符按钮
    operator: "bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-4 px-4 rounded-xl text-lg transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-blue-400/50 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95",
    
    // 功能按钮
    function: "bg-white/40 dark:bg-white/5 backdrop-blur-md border border-white/20 dark:border-white/10 hover:bg-white/60 dark:hover:bg-white/15 text-gray-700 dark:text-gray-300 font-bold py-4 px-4 rounded-xl text-lg transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-gray-400/30 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95",
    
    // 清除按钮
    clear: "bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-4 px-4 rounded-xl text-lg transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-red-400/50 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95",
    
    // 等号按钮
    equals: "bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-4 px-4 rounded-xl text-lg transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-green-400/50 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95",
    
    // 科学计算器专用按钮 (紧凑玻璃风格)
    scientific: {
      // 数字按钮 (科学计算器)
      number: "bg-white/50 dark:bg-white/8 backdrop-blur-md border border-white/20 dark:border-white/15 hover:bg-white/70 dark:hover:bg-white/15 text-gray-800 dark:text-white font-bold py-3 px-2 rounded-lg text-sm transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500/30 shadow-sm hover:shadow-md transform hover:scale-105 active:scale-95",
      
      // 运算符按钮 (科学计算器)
      operator: "bg-gradient-to-br from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-bold py-3 px-2 rounded-lg text-sm transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-blue-400/50 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95",
      
      // 科学函数按钮
      sciFunction: "bg-gradient-to-br from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-bold py-3 px-2 rounded-lg text-sm transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-purple-400/50 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95",
      
      // 内存按钮
      memory: "bg-gradient-to-br from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-bold py-3 px-2 rounded-lg text-sm transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-orange-400/50 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95",
      
      // 功能按钮 (科学计算器)
      function: "bg-white/30 dark:bg-white/5 backdrop-blur-md border border-white/15 dark:border-white/10 hover:bg-white/50 dark:hover:bg-white/12 text-gray-700 dark:text-gray-300 font-bold py-3 px-2 rounded-lg text-sm transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-gray-400/30 shadow-sm hover:shadow-md transform hover:scale-105 active:scale-95",
      
      // 清除按钮 (科学计算器)
      clear: "bg-gradient-to-br from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white font-bold py-3 px-2 rounded-lg text-sm transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-red-400/50 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95",
      
      // 等号按钮 (科学计算器)
      equals: "bg-gradient-to-br from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-bold py-3 px-2 rounded-lg text-sm transition-all duration-150 focus:outline-none focus:ring-2 focus:ring-green-400/50 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95",
      
      // 模式切换按钮
      mode: "bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-bold py-2 px-4 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-400/50 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]",
    }
  }
} as const;

// 状态颜色样式 - 通透玻璃效果
export const statusStyles = {
  // 信息卡片
  info: {
    container: "bg-gradient-to-br from-blue-50/80 to-blue-100/60 dark:from-blue-900/20 dark:to-blue-800/10 backdrop-blur-lg border border-blue-200/50 dark:border-blue-700/30 rounded-xl p-6 shadow-lg ring-1 ring-blue-200/20 dark:ring-blue-700/20",
    title: "text-xl font-semibold text-blue-800 dark:text-blue-200 mb-4",
    text: "text-blue-700 dark:text-blue-300",
    textSmall: "text-sm text-blue-600 dark:text-blue-400",
  },
  
  // 成功/绿色
  success: {
    container: "bg-gradient-to-br from-green-50/80 to-green-100/60 dark:from-green-900/20 dark:to-green-800/10 backdrop-blur-lg border border-green-200/50 dark:border-green-700/30 rounded-xl p-6 shadow-lg ring-1 ring-green-200/20 dark:ring-green-700/20",
    title: "text-xl font-semibold text-green-800 dark:text-green-200 mb-4",
    text: "text-green-700 dark:text-green-300",
    textSmall: "text-sm text-green-600 dark:text-green-400",
  },
  
  // 警告/黄色
  warning: {
    container: "bg-gradient-to-br from-amber-50/80 to-amber-100/60 dark:from-amber-900/20 dark:to-amber-800/10 backdrop-blur-lg border border-amber-200/50 dark:border-amber-700/30 rounded-xl p-6 shadow-lg ring-1 ring-amber-200/20 dark:ring-amber-700/20",
    title: "text-xl font-semibold text-amber-800 dark:text-amber-200 mb-4",
    text: "text-amber-700 dark:text-amber-300",
    textSmall: "text-sm text-amber-600 dark:text-amber-400",
  },
  
  // 错误/橙色
  danger: {
    container: "bg-gradient-to-br from-red-50/80 to-red-100/60 dark:from-red-900/20 dark:to-red-800/10 backdrop-blur-lg border border-red-200/50 dark:border-red-700/30 rounded-xl p-6 shadow-lg ring-1 ring-red-200/20 dark:ring-red-700/20",
    title: "text-xl font-semibold text-red-800 dark:text-red-200 mb-4",
    text: "text-red-700 dark:text-red-300",
    textSmall: "text-sm text-red-600 dark:text-red-400",
  },
  
  // 紫色
  purple: {
    container: "bg-gradient-to-br from-purple-50/80 to-purple-100/60 dark:from-purple-900/20 dark:to-purple-800/10 backdrop-blur-lg border border-purple-200/50 dark:border-purple-700/30 rounded-xl p-6 shadow-lg ring-1 ring-purple-200/20 dark:ring-purple-700/20",
    title: "text-xl font-semibold text-purple-800 dark:text-purple-200 mb-4",
    text: "text-purple-700 dark:text-purple-300",
    textSmall: "text-sm text-purple-600 dark:text-purple-400",
  },
} as const;

// 边框样式
export const borderStyles = {
  left: {
    blue: "border-l-4 border-blue-500",
    green: "border-l-4 border-green-500",
    orange: "border-l-4 border-orange-500",
    purple: "border-l-4 border-purple-500",
    red: "border-l-4 border-red-500",
  },
} as const;

// 工具函数：组合样式类
export const cn = (...classes: (string | undefined | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};